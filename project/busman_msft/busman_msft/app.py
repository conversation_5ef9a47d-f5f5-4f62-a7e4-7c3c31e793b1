"""Main application for the Bus Model Manager."""

import streamlit as st
from busman_msft.components.engine_config import render as render_engine_config
from busman_msft.components.model_detail import render as render_model_detail
from busman_msft.components.model_list import ModelList
from busman_msft.manager import Model<PERSON>anager
from busman_msft.service.engine_service import EngineService
from omom_msft.manager import ModelManager as OMOMModelManager


@st.cache_data
def get_model_manager():
    """Cached model loading function to avoid duplicate loading."""
    try:
        # Use the new unified ModelManager instead of load_all_models
        manager = ModelManager(
            enable_deduplication=True,
            priority_source="model_registry",
            auto_load=True,
        )
        return manager
    except Exception as e:
        st.error(f"Error loading models: {e}")
        return None


def main():
    """Initialize and render the application."""
    st.set_page_config(page_title="Bus Model Manager", page_icon="🚌", layout="wide")

    # Initialize omom model manager
    omom_model_manager = OMOMModelManager()
    engine_service = EngineService(omom_model_manager)

    if "model_manager" not in st.session_state:
        st.session_state.model_manager = get_model_manager()

    model_manager = st.session_state.model_manager
    unified_models = model_manager.get_all_models()

    # Create model name list for the UI
    model_names = model_manager.list_models_names()
    # Sort model names alphabetically
    model_names = sorted(model_names)

    # Initialize session state and select first model if none selected
    if "selected_model" not in st.session_state:
        st.session_state.selected_model = model_names[0] if model_names else None

    # Create two columns that will automatically stack on narrow screens
    col_list, col_details = st.columns([1, 3])

    # Show model list in the first column
    with col_list:
        st.write("### Available Models")
        model_list = ModelList()
        selected_model = model_list.render(
            models=model_names, selected_model=st.session_state.get("selected_model")
        )

        if selected_model and selected_model != st.session_state.selected_model:
            st.session_state.selected_model = selected_model
            st.rerun()

    # Show detail view in the second column
    if st.session_state.selected_model:
        with col_details:
            selected_display_name = st.session_state.selected_model
            model_data = model_manager.get_model_by_name(selected_display_name)

            if model_data:
                render_model_detail(model_data)

                # Render engine configuration
                render_engine_config(engine_service, model_data.name, omom_model_manager)

            else:
                st.error(f"Cannot find model data for: {selected_display_name}")
                st.write(
                    f"Available models: {[getattr(m, 'name', 'Unknown') for m in unified_models]}"
                )


if __name__ == "__main__":
    main()
