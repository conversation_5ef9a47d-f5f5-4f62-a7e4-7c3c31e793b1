# 路径参数使用说明

## 概述

现在 proxy.py 支持两种调用方式：

1. **传统方式**（向后兼容）：使用默认配置
2. **新方式**：通过路径参数指定自定义配置

## API 端点

### 1. 传统端点（向后兼容）
```
POST /v1/chat/completions
```
使用配置文件或环境变量中的默认 BUS_USER、BUS_TOPIC、BUS_RENDERER 参数。

### 2. 新的参数化端点
```
POST /v1/{user}:{topic}:{renderer}/chat/completions
```
从路径中解析 user、topic、renderer 参数。

## 路径参数格式

路径参数格式：`{user}:{topic}:{renderer}`

- **user**: BUS_USER 参数
- **topic**: BUS_TOPIC 参数（可以包含冒号，如 az:// URL）
- **renderer**: BUS_RENDERER 参数

## 使用示例

### 示例 1：基本用法
```bash
curl -X POST "http://localhost:8500/v1/evaluation:simple-topic:harmony_v4.0.16/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "codex-local",
    "messages": [
      {"role": "user", "content": "Hello, world!"}
    ]
  }'
```

### 示例 2：包含 az:// URL 的 topic
```bash
curl -X POST "http://localhost:8500/v1/evaluation:az://orngcresco/twapi/mini/e/model:harmony_v4.0.16/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "codex-local",
    "messages": [
      {"role": "user", "content": "Hello, world!"}
    ]
  }'
```

### 示例 3：流式响应
```bash
curl -X POST "http://localhost:8500/v1/myuser:mytopic:myrenderer/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "codex-local",
    "messages": [
      {"role": "user", "content": "Hello, world!"}
    ],
    "stream": true
  }'
```

## 错误处理

如果路径参数格式不正确，系统会：
1. 记录错误日志
2. 自动回退到默认配置
3. 继续处理请求

无效格式示例：
- `user:topic`（缺少 renderer）
- `user`（缺少 topic 和 renderer）
- `::` （空参数）
- `user::renderer`（空 topic）

## 技术实现

- 使用 FastAPI 的路径参数功能
- 智能解析包含冒号的 topic（从右侧分割 renderer，从左侧分割 user）
- 组件缓存机制，避免重复初始化相同配置
- 完全向后兼容现有 API

## 注意事项

1. 路径参数中的空格会被自动去除
2. 所有参数都不能为空
3. topic 可以包含冒号（如 az:// URL），但 user 和 renderer 不应包含冒号
4. 系统会为每个唯一的配置组合缓存组件，提高性能
